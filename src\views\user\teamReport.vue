<template>
  <div class="PageBox">
      <!-- 顶部用户信息区域 -->
      <div class="header-section">
        <div class="user-info-header">
          <div class="user-avatar-section">
            <div class="user-avatar">
              <!-- 用户头像 -->
              <img
                v-if="UserInfo && UserInfo.header"
                :src="`./static/head/${UserInfo.header}`"
                alt="用户头像"
                class="avatar-img"
              />
              <div v-else class="avatar-placeholder">
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                  <circle cx="20" cy="20" r="20" fill="#f0f0f0" />
                  <path
                    d="M20 20C22.21 20 24 18.21 24 16C24 13.79 22.21 12 20 12C17.79 12 16 13.79 16 16C16 18.21 17.79 20 20 20ZM20 22C16.67 22 10 23.34 10 26.67V28H30V26.67C30 23.34 23.33 22 20 22Z"
                    fill="#ccc"
                  />
                </svg>
              </div>
            </div>
            <div class="user-details">
              <div class="user-name">
                {{ reportData.userName || $t("teamReport.userDefault") }}
                <span class="user-id"
                  >{{ $t("teamReport.groupId") }}:
                  {{ reportData.userIdcode || "000000" }}</span
                >
              </div>
              <div class="team-total">
                {{ $t("teamReport.default[7]") }}:
                <span class="total-count">{{
                  formatLargeNumber(reportData.teamNumber)
                }}</span>
              </div>
            </div>
          </div>
          <div class="chart-icon">
            <div class="chart-bars">
              <div class="bar bar1"></div>
              <div class="bar bar2"></div>
              <div class="bar bar3"></div>
            </div>
          </div>
        </div>

        <!-- 收益统计卡片 -->
        <div class="earnings-card">
          <div
            class="earnings-item"
            @click="
              showEarningsDetail(
                'teamSpread',
                reportData.teamSpread,
                $t('teamReport.default[1]')
              )
            "
          >
            <div
              class="earnings-amount"
              :title="
                `${InitData.currency}${(reportData.teamSpread || 0).toFixed(2)}`
              "
            >
              {{ formatCurrency(reportData.teamSpread) }}
            </div>
            <div class="earnings-label">{{ $t("teamReport.default[1]") }}</div>
          </div>
          <div
            class="earnings-item"
            @click="
              showEarningsDetail(
                'teamTaskRebate',
                reportData.teamTaskRebate,
                $t('teamReport.default[2]')
              )
            "
          >
            <div
              class="earnings-amount"
              :title="
                `${InitData.currency}${(reportData.teamTaskRebate || 0).toFixed(
                  2
                )}`
              "
            >
              {{ formatCurrency(reportData.teamTaskRebate) }}
            </div>
            <div class="earnings-label">{{ $t("teamReport.default[2]") }}</div>
          </div>
          <div
            class="earnings-item"
            @click="
              showEarningsDetail(
                'teamRecharge',
                reportData.teamRecharge,
                $t('teamReport.default[3]')
              )
            "
          >
            <div
              class="earnings-amount"
              :title="
                `${InitData.currency}${(reportData.teamRecharge || 0).toFixed(
                  2
                )}`
              "
            >
              {{ formatCurrency(reportData.teamRecharge) }}
            </div>
            <div class="earnings-label">{{ $t("teamReport.default[3]") }}</div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 团队结构 -->
        <div class="team-structure-section">
          <div class="section-title">{{ $t("teamReport.structure") }}</div>
          <div class="team-levels">
            <div
              class="team-level-item level-1"
              :class="{ active: selectedTeamLevel === 1 }"
              @click.stop="toggleTeamLevel(1)"
            >
              <div class="level-icon">
                <!-- 一级人形图标 -->
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z"
                    fill="#FF4757"
                  />
                </svg>
              </div>
              <div
                class="level-count"
                :title="
                  ((reportData.team1 && reportData.team1.count) || 0).toString()
                "
              >
                {{
                  formatLargeNumber(
                    (reportData.team1 && reportData.team1.count) || 0
                  )
                }}
              </div>
              <div class="level-label">{{ $t("teamReport.team[0]") }}</div>
            </div>
            <div
              class="team-level-item level-2"
              :class="{ active: selectedTeamLevel === 2 }"
              @click="toggleTeamLevel(2)"
            >
              <div class="level-icon">
                <!-- 二级人形图标 -->
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z"
                    fill="#5352ED"
                  />
                </svg>
              </div>
              <div
                class="level-count"
                :title="
                  ((reportData.team2 && reportData.team2.count) || 0).toString()
                "
              >
                {{
                  formatLargeNumber(
                    (reportData.team2 && reportData.team2.count) || 0
                  )
                }}
              </div>
              <div class="level-label">{{ $t("teamReport.team[1]") }}</div>
            </div>
            <div
              class="team-level-item level-3"
              :class="{ active: selectedTeamLevel === 3 }"
              @click="toggleTeamLevel(3)"
            >
              <div class="level-icon">
                <!-- 三级人形图标 -->
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z"
                    fill="#FFA502"
                  />
                </svg>
              </div>
              <div
                class="level-count"
                :title="
                  ((reportData.team3 && reportData.team3.count) || 0).toString()
                "
              >
                {{
                  formatLargeNumber(
                    (reportData.team3 && reportData.team3.count) || 0
                  )
                }}
              </div>
              <div class="level-label">{{ $t("teamReport.team[2]") }}</div>
            </div>
          </div>
        </div>

        <!-- 下属明细列表 -->
        <div class="subordinate-details">
          <div class="section-title">{{ $t("teamReport.details") }}</div>
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-state">
            <div class="loading-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <circle
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="#ff4757"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-dasharray="31.416"
                  stroke-dashoffset="31.416"
                >
                  <animate
                    attributeName="stroke-dasharray"
                    dur="2s"
                    values="0 31.416;15.708 15.708;0 31.416"
                    repeatCount="indefinite"
                  />
                  <animate
                    attributeName="stroke-dashoffset"
                    dur="2s"
                    values="0;-15.708;-31.416"
                    repeatCount="indefinite"
                  />
                </circle>
              </svg>
            </div>
            <div class="loading-text">{{ $t("teamReport.loading") }}...</div>
          </div>
          <div
            class="subordinate-list"
            v-else-if="currentMemberList && currentMemberList.length > 0"
          >
            <div
              class="subordinate-item"
              v-for="(member, index) in currentMemberList"
              :key="member.id || index"
              @click="showMemberDetail(member)"
            >
              <div class="subordinate-info">
                <div class="subordinate-name">
                  {{ member.username || $t("teamReport.userDefault") }}
                </div>
              </div>
              <div
                class="subordinate-commission"
                :title="
                  `${$t('teamReport.commission')}: ${InitData.currency}${(
                    member.total_rebate_all || 0
                  ).toFixed(2)}`
                "
              >
                {{ $t("teamReport.commission") }}: {{ formatCurrency(member.total_rebate_all) }}
              </div>
              <div class="click-hint">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M9 18L15 12L9 6"
                    stroke="#999"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
          </div>
          <!-- 空状态提示 -->
          <div class="empty-state" v-else>
            <div class="empty-icon">
              <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                <circle cx="24" cy="24" r="24" fill="#f5f5f5" />
                <path
                  d="M24 24C26.21 24 28 22.21 28 20C28 17.79 26.21 16 24 16C21.79 16 20 17.79 20 20C20 22.21 21.79 24 24 24ZM24 26C20.67 26 14 27.34 14 30.67V32H34V30.67C34 27.34 27.33 26 24 26Z"
                  fill="#ccc"
                />
              </svg>
            </div>
            <div class="empty-text">{{ $t("teamReport.noData") }}</div>
          </div>
        </div>
      </div>
    <Footer />

    <!-- 成员详情弹窗 -->
    <van-popup
      v-model="showMemberDetailPopup"
      position="bottom"
      :style="{ height: '70%' }"
      round
      closeable
      close-icon-position="top-right"
    >
      <div class="member-detail-popup">
        <div class="popup-header">
          <h3 class="popup-title">{{ $t("teamReport.memberDetail") }}</h3>
        </div>

        <div class="popup-content" v-if="selectedMember">
          <!-- 基本信息 -->
          <div class="detail-section">
            <div class="section-title">{{ $t("teamReport.basicInfo") }}</div>
            <div class="detail-item">
              <span class="detail-label">{{ $t("teamReport.username") }}:</span>
              <span class="detail-value">{{
                selectedMember.username || $t("teamReport.userDefault")
              }}</span>
            </div>

            <div class="detail-item" v-if="selectedMember.phone">
              <span class="detail-label">{{ $t("teamReport.phone") }}:</span>
              <span class="detail-value">{{ selectedMember.phone }}</span>
            </div>
            <div class="detail-item" v-if="selectedMember.email">
              <span class="detail-label">{{ $t("teamReport.email") }}:</span>
              <span class="detail-value">{{ selectedMember.email }}</span>
            </div>
            <div class="detail-item" v-if="selectedMember.reg_time">
              <span class="detail-label">{{ $t("teamReport.regTime") }}:</span>
              <span class="detail-value">{{
                selectedMember.reg_time_formatted ||
                  formatDate(selectedMember.reg_time)
              }}</span>
            </div>
            <div
              class="detail-item"
              v-if="
                selectedMember.inviter_info &&
                  selectedMember.inviter_info.username
              "
            >
              <span class="detail-label">{{ $t("teamReport.invitor") }}:</span>
              <span class="detail-value">{{
                selectedMember.inviter_info.username
              }}</span>
            </div>
          </div>

          <!-- 收益信息 -->
          <div class="detail-section">
            <div class="section-title">{{ $t("teamReport.earningsInfo") }}</div>
            <div class="detail-item">
              <span class="detail-label">{{ $t("teamReport.referralRebate") }}</span>
              <span class="detail-value earnings"
                >{{ InitData.currency
                }}{{
                  Number(selectedMember.referral_rebate || 0).toFixed(2)
                }}</span
              >
            </div>

            <div class="detail-item">
              <span class="detail-label">{{ $t("teamReport.taskRebate") }}</span>
              <span class="detail-value earnings"
                >{{ InitData.currency
                }}{{
                  Number(selectedMember.task_rebate || 0).toFixed(2)
                }}</span
              >
            </div>
            <!-- <div class="detail-item" v-if="selectedMember.balance != null && selectedMember.balance !== undefined">
              <span class="detail-label">{{ $t('teamReport.balance') }}:</span>
              <span class="detail-value">{{ InitData.currency }}{{ Number(selectedMember.balance || 0).toFixed(2) }}</span>
            </div> -->
          </div>

          <!-- 状态信息 -->
          <div class="detail-section">
            <div class="section-title">{{ $t("teamReport.statusInfo") }}</div>
            <div class="detail-item">
              <span class="detail-label">{{ $t("teamReport.status") }}:</span>
              <span
                class="detail-value"
                :class="getStatusClass(selectedMember.state)"
              >
                {{
                  selectedMember.state_text ||
                    getStatusText(selectedMember.state)
                }}
              </span>
            </div>
            <div class="detail-item" v-if="selectedMember.vip_level">
              <span class="detail-label">{{ $t("teamReport.vipLevel") }}:</span>
              <span class="detail-value vip">{{
                selectedMember.vip_name || `VIP${selectedMember.vip_level}`
              }}</span>
            </div>
            <div class="detail-item" v-if="selectedMember.last_login_time">
              <span class="detail-label"
                >{{ $t("teamReport.lastLogin") }}:</span
              >
              <span class="detail-value">{{
                formatDate(selectedMember.last_login_time)
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Footer from "@/components/Footer.vue";

export default {
  name: "TeamReport",
  components: {
    Footer,
  },
  props: [],
  data() {
    return {
      tabsIndex: 0,
      currentDate: "",
      startDate: this.$Util.DateFormat("YY-MM-DD", new Date()),
      endDate: this.$Util.DateFormat("YY-MM-DD", new Date()),
      showDate: false,
      minDate: new Date(2020, 0, 1),
      pickType: 1,
      reportData: {
        teamBalance: 0,
        teamProfit: 0,
        teamWithdrawal: 0,
        directlyUnder: 0,
        firstRechargeToday: 0,
        teamNumber: 0,
        newReg: 0,
        teamSpread: 0, // 推荐收入
        teamTaskRebate: 0, // 任务返佣
        teamRecharge: 0, // 充值返佣
        userName: "",
        userId: "",
        userIdcode: "",
        team1: {
          teamRechargeCount: 0,
          teamRechargeNumber: 0,
          teamSpreadSum: 0,
          count: 0,
          userList: [],
        },
        team2: {
          teamRechargeCount: 0,
          teamRechargeNumber: 0,
          teamSpreadSum: 0,
          count: 0,
          userList: [],
        },
        team3: {
          teamRechargeCount: 0,
          teamRechargeNumber: 0,
          teamSpreadSum: 0,
          count: 0,
          userList: [],
        },
        memberList: [],
      },
      lowerName: [],
      currPid: "",
      teamActive: 0,
      isLoading: false,
      isRefreshing: false,
      // 成员详情弹窗相关
      showMemberDetailPopup: false,
      selectedMember: null,
      // 团队等级选择
      selectedTeamLevel: null, // null表示显示全部，1/2/3表示显示对应等级
    };
  },
  computed: {
    // 根据选中的团队等级返回对应的用户列表
    currentMemberList() {
      console.log(
        "计算currentMemberList, selectedTeamLevel:",
        this.selectedTeamLevel
      );
      if (!this.reportData || !this.reportData.memberList) {
        console.log("reportData或memberList为空");
        return [];
      }

      let result = [];

      // 如果没有选中特定等级，显示全部
      if (!this.selectedTeamLevel) {
        result = this.reportData.memberList || [];
        console.log("返回全部成员列表:", result.length, "个用户");
        return result;
      }

      // 首先尝试从API返回的分级数据中获取
      switch (this.selectedTeamLevel) {
        case 1:
          if (
            this.reportData.team1 &&
            this.reportData.team1.userList &&
            this.reportData.team1.userList.length > 0
          ) {
            result = this.reportData.team1.userList;
            console.log("从team1.userList返回:", result.length, "个用户");
          } else {
            // 如果API没有返回分级数据，从memberList中过滤
            result = this.reportData.memberList.filter(
              (member) => member.level === 1 || member.team_level === 1
            );
            console.log(
              "从memberList过滤level=1用户:",
              result.length,
              "个用户"
            );
          }
          break;
        case 2:
          if (
            this.reportData.team2 &&
            this.reportData.team2.userList &&
            this.reportData.team2.userList.length > 0
          ) {
            result = this.reportData.team2.userList;
            console.log("从team2.userList返回:", result.length, "个用户");
          } else {
            result = this.reportData.memberList.filter(
              (member) => member.level === 2 || member.team_level === 2
            );
            console.log(
              "从memberList过滤level=2用户:",
              result.length,
              "个用户"
            );
          }
          break;
        case 3:
          if (
            this.reportData.team3 &&
            this.reportData.team3.userList &&
            this.reportData.team3.userList.length > 0
          ) {
            result = this.reportData.team3.userList;
            console.log("从team3.userList返回:", result.length, "个用户");
          } else {
            result = this.reportData.memberList.filter(
              (member) => member.level === 3 || member.team_level === 3
            );
            console.log(
              "从memberList过滤level=3用户:",
              result.length,
              "个用户"
            );
          }
          break;
        default:
          result = this.reportData.memberList || [];
          console.log("返回全部成员列表:", result.length, "个用户");
          break;
      }
      return result;
    },
  },
  watch: {
    // 监听用户信息变化
    UserInfo: {
      handler(newVal) {
        if (newVal) {
          this.initUserData();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.initUserData();
    this.getTeamReport();
  },
  mounted() {},
  activated() {},
  destroyed() {},
  methods: {
    // 格式化大数值显示（使用全球化简写）
    formatLargeNumber(value) {
      return this.formatCompactNumber(value, {
        maximumFractionDigits: 0, // 人数等不需要小数点
      });
    },

    // 格式化货币显示（使用全球化简写）
    formatCurrency(value) {
      if (!value || value === 0) return "0.00";

      const num = parseFloat(value);
      if (isNaN(num)) return "0.00";

      // 如果数值小于100，显示完整金额
      if (Math.abs(num) < 100) {
        return num.toFixed(2);
      }

      // 如果数值小于1000，显示为整数
      if (Math.abs(num) < 1000) {
        return Math.round(num).toString();
      }

      // 如果数值大于等于1000，使用全球化简写格式
      return this.formatCompactNumber(num, {
        maximumFractionDigits: 1,
      });
    },

    // 初始化用户数据
    initUserData() {
      if (this.UserInfo) {
        this.reportData.userName =
          this.UserInfo.username || this.$t("teamReport.userDefault");
        this.reportData.userId = this.UserInfo.userid || "000000";
        this.reportData.userIdcode = this.UserInfo.idcode || "000000";
      }
    },

    // 切换团队等级显示
    toggleTeamLevel(level) {
      console.log("点击团队等级:", level, "当前选中:", this.selectedTeamLevel);
      // 如果点击的是当前选中的等级，则恢复显示全部
      if (this.selectedTeamLevel === level) {
        this.selectedTeamLevel = null;
        console.log("取消选中，恢复显示全部");
      } else {
        this.selectedTeamLevel = level;
        console.log("选中等级:", level);
      }
      console.log("更新后的selectedTeamLevel:", this.selectedTeamLevel);
    },

    // 显示收益详情弹窗
    showEarningsDetail(type, value, label) {
      const fullValue = `${this.InitData.currency}${(value || 0).toFixed(2)}`;
      this.$dialog.alert({
        title: label,
        message: `${this.$t("dialog[7]")}：${fullValue}`,
        confirmButtonText: this.$t("dialog[1]"),
        className: "earnings-detail-dialog",
      });
    },

    // 显示成员详情
    showMemberDetail(member) {
      console.log("显示成员详情:", member);
      if (!member) {
        console.error("成员数据为空");
        return;
      }
      // 确保所有数值字段都是安全的
      this.selectedMember = {
        ...member,
        referral_rebate: member.referral_rebate || 0,
        task_rebate: member.task_rebate || 0,
        total_rebate_all: member.total_rebate_all || 0,
        total_recharge: member.total_recharge || 0,
        balance: member.balance || 0,
        direct_count: member.direct_count || 0,
        team_count: member.team_count || 0,
        vip_level: member.vip_level || 0,
        vip_name: member.vip_name || "",
        state: member.state !== undefined ? member.state : null,
        state_text: member.state_text || "",
        inviter_info: member.inviter_info || null,
      };
      this.showMemberDetailPopup = true;
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return "---";
      try {
        const date = new Date(dateString);
        return date.toLocaleDateString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        });
      } catch (error) {
        return dateString;
      }
    },

    // 获取状态文本
    getStatusText(state) {
      console.log("状态值:", state, "类型:", typeof state);
      // 转换为字符串进行比较，确保兼容性
      const stateStr = String(state);
      const statusMap = {
        "1": this.$t("teamReport.statusActive"), // 正常
        "0": this.$t("teamReport.statusInactive"), // 未激活
        "2": this.$t("teamReport.statusSuspended"), // 暂停
        "-1": this.$t("teamReport.statusBanned"), // 封禁
        null: this.$t("teamReport.statusUnknown"),
        undefined: this.$t("teamReport.statusUnknown"),
      };
      return statusMap[stateStr] || this.$t("teamReport.statusUnknown");
    },

    // 获取状态样式类
    getStatusClass(state) {
      // 转换为字符串进行比较，确保兼容性
      const stateStr = String(state);
      const classMap = {
        "1": "status-active", // 正常状态 - 绿色
        "0": "status-inactive", // 未激活 - 灰色
        "2": "status-suspended", // 暂停 - 橙色
        "-1": "status-banned", // 封禁 - 红色
      };
      return classMap[stateStr] || "status-unknown";
    },

    confirmDate(date) {
      if (this.pickType == 1) {
        this.startDate = this.$Util.DateFormat("YY-MM-DD", date);
      }
      if (this.pickType == 2) {
        this.endDate = this.$Util.DateFormat("YY-MM-DD", date);
      }
      this.showDate = false;
    },
    changeTabs(index) {
      switch (index) {
        case 1:
          break;
        default:
          this.currPid = "";
          this.lowerName = [];
          this.getTeamReport();
      }
    },
    getSubUser(pid, name) {
      this.lowerName.push({ name: name, pve_id: pid });
      this.getMyTeam(pid);
    },
    getTeamReport() {
      this.isLoading = true;
      let post = { startdate: this.startDate, enddate: this.endDate };
      if (this.currPid) {
        post.pve_id = this.currPid;
      }

      console.log("获取团队报告，参数:", post);

      this.$Model.TeamReport(post, (data) => {
        this.isLoading = false;
        console.log("团队报告API响应:", data);
        console.log("API返回的team1:", data.team1);
        console.log("API返回的team2:", data.team2);
        console.log("API返回的team3:", data.team3);
        console.log("API返回的memberList:", data.memberList);

        if (data.code == 1) {
          // 合并用户信息和API数据
          const updatedData = {
            ...this.reportData,
            teamBalance: data.teamBalance || 0,
            teamProfit: data.teamProfit || 0,
            teamWithdrawal: data.teamWithdrawal || 0,
            directlyUnder: data.directlyUnder || 0,
            firstRechargeToday: data.firstRechargeToday || 0,
            teamNumber: data.teamNumber || 0,
            newReg: data.newReg || 0,
            teamSpread: data.teamSpread || 0, // 推荐收入
            teamTaskRebate: data.teamTaskRebate || 0, // 任务返佣
            teamRecharge: data.teamRecharge || 0, // 充值返佣
            memberList: data.memberList || [],
            pagination: data.pagination || null,
            userName:
              (this.UserInfo && this.UserInfo.username) ||
              data.userName ||
              this.$t("teamReport.userDefault"),
            userId:
              (this.UserInfo && this.UserInfo.userid) ||
              data.userId ||
              "000000",
            userIdcode:
              (this.UserInfo && this.UserInfo.idcode) ||
              data.userIdcode ||
              "000000",
            team1: {
              teamRechargeCount:
                (data.team1 && data.team1.teamRechargeCount) || 0,
              teamRechargeNumber:
                (data.team1 && data.team1.teamRechargeNumber) || 0,
              teamSpreadSum: (data.team1 && data.team1.teamSpreadSum) || 0,
              count: (data.team1 && data.team1.count) || 0,
              userList: (data.team1 && data.team1.userList) || [],
            },
            team2: {
              teamRechargeCount:
                (data.team2 && data.team2.teamRechargeCount) || 0,
              teamRechargeNumber:
                (data.team2 && data.team2.teamRechargeNumber) || 0,
              teamSpreadSum: (data.team2 && data.team2.teamSpreadSum) || 0,
              count: (data.team2 && data.team2.count) || 0,
              userList: (data.team2 && data.team2.userList) || [],
            },
            team3: {
              teamRechargeCount:
                (data.team3 && data.team3.teamRechargeCount) || 0,
              teamRechargeNumber:
                (data.team3 && data.team3.teamRechargeNumber) || 0,
              teamSpreadSum: (data.team3 && data.team3.teamSpreadSum) || 0,
              count: (data.team3 && data.team3.count) || 0,
              userList: (data.team3 && data.team3.userList) || [],
            },
          };

          this.reportData = updatedData;
          console.log("团队数据更新成功:", this.reportData);
        } else {
          console.log("团队报告获取失败:", data.code_dec);
          // 保持用户信息，但重置团队数据为0
          this.reportData = {
            ...this.reportData,
            teamBalance: 0,
            teamProfit: 0,
            teamWithdrawal: 0,
            directlyUnder: 0,
            firstRechargeToday: 0,
            teamNumber: 0,
            newReg: 0,
            teamSpread: 0, // 推荐收入
            teamTaskRebate: 0, // 任务返佣
            teamRecharge: 0, // 充值返佣
            team1: {
              teamRechargeCount: 0,
              teamRechargeNumber: 0,
              teamSpreadSum: 0,
              count: 0,
              userList: [],
            },
            team2: {
              teamRechargeCount: 0,
              teamRechargeNumber: 0,
              teamSpreadSum: 0,
              count: 0,
              userList: [],
            },
            team3: {
              teamRechargeCount: 0,
              teamRechargeNumber: 0,
              teamSpreadSum: 0,
              count: 0,
              userList: [],
            },
          };
        }
      });
    },
    getMyTeam(pid) {
      this.isLoading = true;
      let post = {
        pve_id: pid,
        startdate: this.startDate,
        enddate: this.endDate,
      };
      this.currPid = pid;

      console.log("获取下级团队，参数:", post);

      this.$Model.TeamReport(post, (data) => {
        this.isLoading = false;
        console.log("下级团队API响应:", data);

        if (data.code == 1) {
          // 更新团队相关数据
          this.reportData.memberList = data.memberList || [];
          console.log("下级团队数据更新成功:", this.reportData.memberList);
        } else {
          console.log("下级团队获取失败:", data.code_dec);
          this.reportData.memberList = [];
        }
      });
    },
    lowerReport(pid, index) {
      if (pid) {
        this.currPid = pid;
        if (index != this.lowerName.length - 1) {
          this.getMyTeam(pid);
          this.lowerName = this.lowerName.slice(0, index + 1);
        }
      } else {
        this.currPid = "";
        this.lowerName = [];
        this.getTeamReport();
      }
    },

    // 获取级别文本
    getLevelText(level) {
      const levelMap = {
        1: this.$t("teamReport.team[0]"),
        2: this.$t("teamReport.team[1]"),
        3: this.$t("teamReport.team[2]"),
      };
      return levelMap[level] || this.$t("teamReport.unknown");
    },

    // 获取级别样式类
    getLevelClass(level) {
      const classMap = {
        1: "level-1",
        2: "level-2",
        3: "level-3",
      };
      return classMap[level] || "";
    },

    // 刷新团队数据
    refreshTeamData() {
      this.initUserData();
      this.getTeamReport();
    },

    // 下拉刷新
    onRefresh() {
      this.refreshTeamData();
      // 模拟刷新延迟
      setTimeout(() => {
        this.isRefreshing = false;
      }, 1000);
    },
  },
};
</script>
<style scoped>
@import url("https://fonts.googleapis.com/css2?family=Source+Han+Sans+SC:wght@300;400;500;700;900&display=swap");

.PageBox {
  background: #ffffff;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-bottom: 3.75rem; /* 60px -> 3.75rem */
  font-family: "Source Han Sans SC", "PingFang SC", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  margin: 0;
  padding-top: 0;
}

.ScrollBox {
  margin: 0;
  padding: 0;
  width: 100%;
  flex: 1;
  height: 0;
  display: flex;
  flex-direction: column;
}

/* 头部区域样式 */
.header-section {
  background: linear-gradient(180deg, #f42937 0%, rgba(244, 41, 55, 0) 100%);
  padding: 1.25rem 1.5rem 0; /* 20px 24px -> 1.25rem 1.5rem */
  position: relative;
  overflow: hidden;
  min-height: 12.5rem; /* 200px -> 12.5rem */
  margin-top: 0;
}

.user-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem; /* 16px -> 1rem */
  position: relative;
  z-index: 2;
}

.user-avatar-section {
  display: flex;
  align-items: center;
  gap: 0.75rem; /* 12px -> 0.75rem */
}

.user-avatar {
  width: 3.75rem; /* 60px -> 3.75rem */
  height: 3.75rem; /* 60px -> 3.75rem */
  border-radius: 50%;
  overflow: hidden;
  border: 0.1875rem solid rgba(255, 255, 255, 0.3); /* 3px -> 0.1875rem */
}

.user-avatar img,
.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
}

.avatar-placeholder.small {
  width: 100%;
  height: 100%;
}

.user-details {
  color: white;
}

.user-name {
  font-size: 1.125rem; /* 18px -> 1.125rem */
  font-weight: 600;
  margin-bottom: 0.25rem; /* 4px -> 0.25rem */
  font-family: "Source Han Sans SC", sans-serif;
}

.user-id {
  font-size: 0.875rem; /* 14px -> 0.875rem */
  opacity: 0.8;
  font-weight: 400;
}

.team-total {
  font-size: 0.875rem; /* 14px -> 0.875rem */
  opacity: 0.9;
  font-family: "PingFang SC", sans-serif;
}

.total-count {
  font-weight: 600;
}

.chart-icon {
  position: relative;
  z-index: 2;
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: 0.25rem; /* 4px -> 0.25rem */
  height: 2.5rem; /* 40px -> 2.5rem */
}

.bar {
  width: 0.5rem; /* 8px -> 0.5rem */
  background: rgba(255, 255, 255, 0.8);
  border-radius: 0.25rem; /* 4px -> 0.25rem */
}

.bar1 {
  height: 1.25rem; /* 20px -> 1.25rem */
}
.bar2 {
  height: 2.1875rem; /* 35px -> 2.1875rem */
}
.bar3 {
  height: 1.5625rem; /* 25px -> 1.5625rem */
}

/* 收益统计卡片 */
.earnings-card {
  background: white;
  border-radius: 1rem; /* 16px -> 1rem */
  padding: 1rem; /* 16px -> 1rem */
  display: flex;
  justify-content: space-between;
  box-shadow: 0 0.25rem 1.25rem rgba(0, 0, 0, 0.1); /* 0 4px 20px -> 0 0.25rem 1.25rem */
  position: relative;
  z-index: 2;
  margin-bottom: 0.75rem; /* 12px -> 0.75rem */
}

.earnings-item {
  text-align: center;
  flex: 1;
}

.earnings-amount {
  font-size: 1.25rem; /* 20px -> 1.25rem */
  font-weight: 700;
  color: #ff4757;
  margin-bottom: 0.5rem; /* 8px -> 0.5rem */
  font-family: "Source Han Sans SC", sans-serif;
  cursor: help;
  transition: all 0.3s ease;
}

.earnings-amount:hover {
  color: #ff2d3a;
  transform: scale(1.05);
}

.earnings-label {
  font-size: 0.8125rem; /* 13px -> 0.8125rem */
  color: #666;
  font-family: "PingFang SC", sans-serif;
}

/* 主要内容区域 */
.main-content {
  padding: 0 1.5rem 0; /* 0 24px -> 0 1.5rem */
  flex: 1;
  height: 0;
  width: 100%;
  display: flex;
  overflow-y: auto;
  flex-direction: column;
}

/* 团队结构样式 */
.team-structure-section {
  background: white;
  border-radius: 1rem; /* 16px -> 1rem */
  padding: 1.25rem; /* 20px -> 1.25rem */
  margin-bottom: 0.75rem; /* 12px -> 0.75rem */
  box-shadow: 0 0.125rem 0.75rem rgba(0, 0, 0, 0.08); /* 0 2px 12px -> 0 0.125rem 0.75rem */
}

.section-title {
  font-size: 1.125rem; /* 18px -> 1.125rem */
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem; /* 16px -> 1rem */
  font-family: "Source Han Sans SC", sans-serif;
}

.team-levels {
  display: flex;
  justify-content: space-between;
  gap: 0.75rem; /* 12px -> 0.75rem */
}

.team-level-item {
  flex: 1;
  text-align: center;
  padding: 0.75rem; /* 12px -> 0.75rem */
  border-radius: 0.75rem; /* 12px -> 0.75rem */
  background: transparent;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 0.125rem solid transparent; /* 2px -> 0.125rem */
}

.team-level-item.active {
  border-color: #ff4757;
  background: rgba(255, 71, 87, 0.05);
  transform: translateY(-0.125rem); /* -2px -> -0.125rem */
  box-shadow: 0 0.25rem 1rem rgba(255, 71, 87, 0.2); /* 0 4px 16px -> 0 0.25rem 1rem */
}

.level-icon {
  width: 2.5rem; /* 40px -> 2.5rem */
  height: 2.5rem; /* 40px -> 2.5rem */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.75rem; /* 0 auto 12px -> 0 auto 0.75rem */
}

.level-1 .level-icon {
  background: rgba(255, 71, 87, 0.1);
}
.level-2 .level-icon {
  background: rgba(83, 82, 237, 0.1);
}
.level-3 .level-icon {
  background: rgba(255, 165, 2, 0.1);
}

.level-count {
  font-size: 1.5rem; /* 24px -> 1.5rem */
  font-weight: 700;
  margin-bottom: 0.5rem; /* 8px -> 0.5rem */
  font-family: "Source Han Sans SC", sans-serif;
  cursor: help;
  transition: all 0.3s ease;
}

.level-count:hover {
  transform: scale(1.1);
}

.level-1 .level-count {
  color: #ff4757;
}
.level-2 .level-count {
  color: #5352ed;
}
.level-3 .level-count {
  color: #ffa502;
}

.level-label {
  font-size: 0.8125rem; /* 13px -> 0.8125rem */
  color: #666;
  font-family: "PingFang SC", sans-serif;
}

/* 下属明细样式 */
.subordinate-details {
  width: 100%;
  height: 0;
  flex: 1;
  min-height: 25rem; /* 400px -> 25rem */
  background: white;
  border-radius: 1rem; /* 16px -> 1rem */
  padding: 1.25rem; /* 20px -> 1.25rem */
  margin-bottom: 1.25rem; /* 20px -> 1.25rem */
  box-shadow: 0 0.125rem 0.75rem rgba(0, 0, 0, 0.08); /* 0 2px 12px -> 0 0.125rem 0.75rem */
  display: flex;
  flex-direction: column;
}

.subordinate-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem; /* 8px -> 0.5rem */
  max-height: 25rem; /* 400px -> 25rem */
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
  height: 0;
  flex: 1;
}

/* 滚动条样式 */
.subordinate-list::-webkit-scrollbar {
  width: 0.25rem; /* 4px -> 0.25rem */
}

.subordinate-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 0.125rem; /* 2px -> 0.125rem */
}

.subordinate-list::-webkit-scrollbar-thumb {
  background: #ff4757;
  border-radius: 0.125rem; /* 2px -> 0.125rem */
}

.subordinate-list::-webkit-scrollbar-thumb:hover {
  background: #d63031;
}

.subordinate-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.25rem; /* 16px 20px -> 1rem 1.25rem */
  background: transparent;
  border-radius: 0.75rem; /* 12px -> 0.75rem */
  transition: all 0.3s ease;
  border-bottom: 0.0625rem solid #f0f0f0; /* 1px -> 0.0625rem */
  cursor: pointer;
  position: relative;
}

.subordinate-item:last-child {
  border-bottom: none;
}

.subordinate-item:hover {
  background: rgba(255, 71, 87, 0.05);
  transform: translateX(0.25rem); /* 4px -> 0.25rem */
  box-shadow: 0 0.125rem 0.5rem rgba(255, 71, 87, 0.1); /* 0 2px 8px -> 0 0.125rem 0.5rem */
}

.subordinate-item:active {
  transform: translateX(0.125rem); /* 2px -> 0.125rem */
  background: rgba(255, 71, 87, 0.08);
}

.subordinate-info {
  flex: 1;
}

.subordinate-name {
  font-size: 1rem; /* 16px -> 1rem */
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem; /* 4px -> 0.25rem */
  font-family: "Source Han Sans SC", sans-serif;
}

.subordinate-level {
  font-size: 0.75rem; /* 12px -> 0.75rem */
  padding: 0.125rem 0.5rem; /* 2px 8px -> 0.125rem 0.5rem */
  border-radius: 0.75rem; /* 12px -> 0.75rem */
  font-weight: 500;
  margin-left: 0.5rem; /* 8px -> 0.5rem */
}

.subordinate-level.level-1 {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
}
.subordinate-level.level-2 {
  background: rgba(83, 82, 237, 0.1);
  color: #5352ed;
}
.subordinate-level.level-3 {
  background: rgba(255, 165, 2, 0.1);
  color: #ffa502;
}

.subordinate-id {
  font-size: 0.8125rem; /* 13px -> 0.8125rem */
  color: #999;
  font-family: "PingFang SC", sans-serif;
}

.subordinate-commission {
  font-size: 1rem; /* 16px -> 1rem */
  font-weight: 600;
  color: #ff4757;
  font-family: "Source Han Sans SC", sans-serif;
  cursor: help;
  transition: all 0.3s ease;
}

.subordinate-commission:hover {
  color: #ff2d3a;
  transform: scale(1.05);
}

/* 加载状态样式 */
.loading-state {
  text-align: center;
  padding: 2.5rem 1.25rem; /* 40px 20px -> 2.5rem 1.25rem */
  color: #999;
}

.loading-icon {
  margin-bottom: 1rem; /* 16px -> 1rem */
  display: flex;
  justify-content: center;
}

.loading-text {
  font-size: 0.875rem; /* 14px -> 0.875rem */
  color: #999;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 2.5rem 1.25rem; /* 40px 20px -> 2.5rem 1.25rem */
  color: #999;
}

.empty-icon {
  margin-bottom: 1rem; /* 16px -> 1rem */
  display: flex;
  justify-content: center;
}

.empty-text {
  font-size: 0.875rem; /* 14px -> 0.875rem */
  color: #999;
}

/* 搜索区域样式 */
.search-container {
  margin: 0 1.5rem 1.25rem; /* 0 24px 20px -> 0 1.5rem 1.25rem */
}

.search {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.25rem; /* 16px 20px -> 1rem 1.25rem */
  background: white;
  border-radius: 0.75rem; /* 12px -> 0.75rem */
  box-shadow: 0 0.125rem 0.75rem rgba(0, 0, 0, 0.08); /* 0 2px 12px -> 0 0.125rem 0.75rem */
  border: 0.0625rem solid #f5f5f5; /* 1px -> 0.0625rem */
}

.date-range {
  display: flex;
  align-items: center;
  gap: 0.75rem; /* 12px -> 0.75rem */
}

.date-input {
  border: 0.0625rem solid #e8e8e8; /* 1px -> 0.0625rem */
  border-radius: 0.5rem; /* 8px -> 0.5rem */
  width: 6.25rem; /* 100px -> 6.25rem */
  height: 2.25rem; /* 36px -> 2.25rem */
  line-height: 2.25rem; /* 36px -> 2.25rem */
  text-align: center;
  font-size: 0.875rem; /* 14px -> 0.875rem */
  color: #333;
  background: #fafafa;
  transition: all 0.3s ease;
  font-family: "PingFang SC", sans-serif;
}

.date-input:focus {
  border-color: #ff0f23;
  background: white;
  outline: none;
  box-shadow: 0 0 0 0.125rem rgba(255, 15, 35, 0.1); /* 0 0 0 2px -> 0 0 0 0.125rem */
}

.date-separator {
  color: #666;
  font-size: 0.875rem; /* 14px -> 0.875rem */
  margin: 0 0.375rem; /* 0 6px -> 0 0.375rem */
  font-weight: 500;
}

.search-btn {
  background: #ff0f23 !important;
  border: none !important;
  border-radius: 0.5rem !important; /* 8px -> 0.5rem */
  height: 2.25rem; /* 36px -> 2.25rem */
  padding: 0 1.25rem; /* 0 20px -> 0 1.25rem */
  font-size: 0.875rem; /* 14px -> 0.875rem */
  font-weight: 500;
  font-family: "PingFang SC", sans-serif;
}

/* 主要统计数据区域 */
.main-stats-section {
  margin: 0 1.5rem 1.5rem; /* 0 24px 24px -> 0 1.5rem 1.5rem */
}

/* 统计卡片样式 */
.stats-card {
  background: white !important;
  border-radius: 0.75rem !important; /* 12px -> 0.75rem */
  margin: 0.375rem !important; /* 6px -> 0.375rem */
  box-shadow: 0 0.125rem 0.75rem rgba(0, 0, 0, 0.08) !important; /* 0 2px 12px -> 0 0.125rem 0.75rem */
  padding: 1.25rem 1rem !important; /* 20px 16px -> 1.25rem 1rem */
  transition: all 0.3s ease !important;
  border: 0.0625rem solid #f5f5f5 !important; /* 1px -> 0.0625rem */
}

.stats-card:hover {
  transform: translateY(-0.125rem); /* -2px -> -0.125rem */
  box-shadow: 0 0.375rem 1.25rem rgba(0, 0, 0, 0.12); /* 0 6px 20px -> 0 0.375rem 1.25rem */
  border-color: #ff0f23;
}

.stats-label {
  color: #666;
  font-weight: 500;
  margin-bottom: 0.75rem; /* 12px -> 0.75rem */
  font-size: 0.8125rem; /* 13px -> 0.8125rem */
  line-height: 1.4;
  font-family: "PingFang SC", sans-serif;
}

.stats-value {
  color: #ff0f23;
  font-size: 1.25rem; /* 20px -> 1.25rem */
  font-weight: 700;
  margin-top: 0.25rem; /* 4px -> 0.25rem */
  font-family: "Source Han Sans SC", sans-serif;
}
/* 分级团队数据区域 */
.team-levels-section {
  margin: 0 1.5rem 1.5rem; /* 0 24px 24px -> 0 1.5rem 1.5rem */
}

.team-level-content {
  padding: 1rem 0; /* 16px 0 -> 1rem 0 */
}

/* 团队统计卡片样式 */
.team-stats-card {
  background: white !important;
  border-radius: 0.5rem !important; /* 8px -> 0.5rem */
  margin: 0.25rem !important; /* 4px -> 0.25rem */
  box-shadow: 0 0.0625rem 0.5rem rgba(0, 0, 0, 0.06) !important; /* 0 1px 8px -> 0 0.0625rem 0.5rem */
  padding: 1rem 0.75rem !important; /* 16px 12px -> 1rem 0.75rem */
  transition: all 0.3s ease !important;
  border: 0.0625rem solid #f5f5f5 !important; /* 1px -> 0.0625rem */
}

.team-stats-label {
  color: #666;
  font-weight: 400;
  margin-bottom: 0.5rem; /* 8px -> 0.5rem */
  font-size: 0.75rem; /* 12px -> 0.75rem */
  line-height: 1.3;
  font-family: "PingFang SC", sans-serif;
}

.team-stats-value {
  color: #ff0f23;
  font-size: 1rem; /* 16px -> 1rem */
  font-weight: 600;
  font-family: "Source Han Sans SC", sans-serif;
}

/* 面包屑导航样式 */
.breadcrumb-nav {
  margin: 0 1.5rem 1rem; /* 0 24px 16px -> 0 1.5rem 1rem */
}

.breadcrumb-container {
  padding: 1rem 1.25rem; /* 16px 20px -> 1rem 1.25rem */
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  background: white;
  border-radius: 0.75rem; /* 12px -> 0.75rem */
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1); /* 0 2px 8px -> 0 0.125rem 0.5rem */
}

.breadcrumb-item {
  color: #666;
  text-decoration: none;
  padding: 0.25rem 0.5rem; /* 4px 8px -> 0.25rem 0.5rem */
  border-radius: 0.25rem; /* 4px -> 0.25rem */
  transition: all 0.3s ease;
  font-family: "PingFang SC", sans-serif;
}

.breadcrumb-item:hover {
  background: #f0f0f0;
}

.breadcrumb-home,
.breadcrumb-link {
  color: #ff0f23;
  font-weight: 600;
}

.breadcrumb-current {
  color: #ff0f23;
  font-weight: 600;
}

.breadcrumb-separator {
  color: #999;
  margin: 0 0.25rem; /* 0 4px -> 0 0.25rem */
}

.breadcrumb-group {
  display: inline-flex;
  align-items: center;
}

/* 团队表格样式 */
.team-table-container {
  margin: 0 1.5rem 1.25rem; /* 0 24px 20px -> 0 1.5rem 1.25rem */
}

.table-wrapper {
  background: white;
  border-radius: 0.75rem; /* 12px -> 0.75rem */
  overflow: hidden;
  box-shadow: 0 0.125rem 0.75rem rgba(0, 0, 0, 0.08); /* 0 2px 12px -> 0 0.125rem 0.75rem */
}

.team-table {
  text-align: center;
  width: 100%;
  background: white;
  border-collapse: collapse;
}

.team-table thead tr {
  background: #f8f9fa;
}

.team-table th {
  padding: 1rem 0.75rem; /* 16px 12px -> 1rem 0.75rem */
  color: #333;
  font-weight: 600;
  border-bottom: 0.0625rem solid #e5e5e5; /* 1px -> 0.0625rem */
  font-size: 0.875rem; /* 14px -> 0.875rem */
  font-family: "PingFang SC", sans-serif;
}

.team-table .table-row {
  transition: all 0.3s ease;
}

.team-table .table-row:hover {
  background: #f8f9fa;
}

.team-table .user-cell {
  padding: 1rem 0.75rem; /* 16px 12px -> 1rem 0.75rem */
  border-bottom: 0.0625rem solid #f0f0f0; /* 1px -> 0.0625rem */
}

.team-table .data-cell {
  padding: 1rem 0.75rem; /* 16px 12px -> 1rem 0.75rem */
  border-bottom: 0.0625rem solid #f0f0f0; /* 1px -> 0.0625rem */
  color: #666;
  font-family: "Source Han Sans SC", sans-serif;
}

.team-table .table-row:last-child .user-cell,
.team-table .table-row:last-child .data-cell {
  border-bottom: none;
}

.user-link {
  color: #ff0f23;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  font-family: "PingFang SC", sans-serif;
}

.user-link:hover {
  color: #d60d1f;
}

/* 标签页样式 */
.TeamReportTabs >>> .van-tabs__nav {
  background: white;
  margin: 0 1.5rem 1.25rem; /* 0 24px 20px -> 0 1.5rem 1.25rem */
  border-radius: 0.75rem; /* 12px -> 0.75rem */
  box-shadow: 0 0.125rem 0.75rem rgba(0, 0, 0, 0.08); /* 0 2px 12px -> 0 0.125rem 0.75rem */
  padding: 0.375rem; /* 6px -> 0.375rem */
}

.TeamReportTabs >>> .van-tab {
  border-radius: 0.5rem; /* 8px -> 0.5rem */
  transition: all 0.3s ease;
  font-weight: 500;
  padding: 0.75rem 1.25rem; /* 12px 20px -> 0.75rem 1.25rem */
  font-family: "PingFang SC", sans-serif;
}

.TeamReportTabs >>> .van-tab--active {
  background: #ff0f23;
  color: #fff;
  border-radius: 0.5rem; /* 8px -> 0.5rem */
  font-weight: 600;
}

.TeamReportTabsItem >>> .van-tabs__nav {
  background: white;
  border-radius: 0.5rem; /* 8px -> 0.5rem */
  margin: 0 0.375rem; /* 0 6px -> 0 0.375rem */
  box-shadow: 0 0.0625rem 0.375rem rgba(0, 0, 0, 0.06); /* 0 1px 6px -> 0 0.0625rem 0.375rem */
}

.TeamReportTabsItem >>> .van-tab {
  font-size: 0.875rem; /* 14px -> 0.875rem */
  font-weight: 500;
  font-family: "PingFang SC", sans-serif;
}

.TeamReportTabsItem >>> .van-tab--active {
  background-color: transparent !important;
  color: #ff0f23;
  font-weight: 600;
  border-bottom: 0.125rem solid #ff0f23; /* 2px -> 0.125rem */
}

/* 网格样式优化 */
.PageBox >>> .van-grid {
  padding: 0 0.375rem; /* 0 6px -> 0 0.375rem */
}

.PageBox >>> .MyEarnings {
  background: transparent;
}

.PageBox >>> .TeamLevelGrid {
  background: transparent;
  padding: 0 0.25rem; /* 0 4px -> 0 0.25rem */
}

.customTabsItem {
  margin: 0 0 1.25rem; /* 0 0 20px -> 0 0 1.25rem */
  background: transparent !important;
  border-radius: 0.75rem; /* 12px -> 0.75rem */
}

/* 空状态样式 */
.empty-state {
  opacity: 0.6;
  padding: 2.5rem 1.25rem; /* 40px 20px -> 2.5rem 1.25rem */
}

/* 日期选择器样式 */
.customDatetimePicker >>> .van-picker__columns {
  margin-top: 3.125rem; /* 50px -> 3.125rem */
}

.PickerPopup {
  background-color: white;
  padding: 1rem; /* 16px -> 1rem */
  margin-top: 1.25rem; /* 20px -> 1.25rem */
  background: #fff url("../../../static/images/NoticePopup.png") no-repeat;
  background-size: 100%;
  border-radius: 2rem 2rem 0 0 !important; /* 32px -> 2rem */
}

.PickerPopup .van-picker {
  background-color: transparent;
}

.PickerPopup .van-picker__mask {
  background: none;
}

.PickerPopup .van-picker-column__item {
  font-size: 0.8rem;
  color: #2c2c2c;
  font-family: "PingFang SC", sans-serif;
}

.PickerPopup .van-picker__confirm,
.PickerPopup .van-picker__cancel {
  color: white;
  font-family: "PingFang SC", sans-serif;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .search-container,
  .main-stats-section,
  .team-levels-section,
  .breadcrumb-nav,
  .team-table-container {
    margin-left: 1.25rem; /* 20px -> 1.25rem */
    margin-right: 1.25rem; /* 20px -> 1.25rem */
  }

  .TeamReportTabs >>> .van-tabs__nav {
    margin: 0 1.25rem 1.25rem; /* 0 20px 20px -> 0 1.25rem 1.25rem */
  }
}

/* 点击提示图标样式 */
.click-hint {
  display: flex;
  align-items: center;
  margin-left: 0.5rem; /* 8px -> 0.5rem */
  opacity: 0.6;
  transition: all 0.3s ease;
}

.subordinate-item:hover .click-hint {
  opacity: 1;
  transform: translateX(0.125rem); /* 2px -> 0.125rem */
}

/* 成员详情弹窗样式 */
.member-detail-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.popup-header {
  background: white;
  padding: 1.25rem; /* 20px -> 1.25rem */
  border-bottom: 0.0625rem solid #eee; /* 1px -> 0.0625rem */
  position: sticky;
  top: 0;
  z-index: 10;
}

.popup-title {
  font-size: 1.125rem; /* 18px -> 1.125rem */
  font-weight: 600;
  color: #333;
  margin: 0;
  text-align: center;
  font-family: "Source Han Sans SC", sans-serif;
}

.popup-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem; /* 16px -> 1rem */
}

.detail-section {
  background: white;
  border-radius: 0.75rem; /* 12px -> 0.75rem */
  padding: 1rem; /* 16px -> 1rem */
  margin-bottom: 0.75rem; /* 12px -> 0.75rem */
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.06); /* 0 2px 8px -> 0 0.125rem 0.5rem */
}

.detail-section .section-title {
  font-size: 1rem; /* 16px -> 1rem */
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem; /* 16px -> 1rem */
  padding-bottom: 0.5rem; /* 8px -> 0.5rem */
  border-bottom: 0.125rem solid #ff4757; /* 2px -> 0.125rem */
  font-family: "Source Han Sans SC", sans-serif;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0; /* 12px 0 -> 0.75rem 0 */
  border-bottom: 0.0625rem solid #f5f5f5; /* 1px -> 0.0625rem */
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 0.875rem; /* 14px -> 0.875rem */
  color: #666;
  font-weight: 500;
  min-width: 5rem; /* 80px -> 5rem */
  font-family: "PingFang SC", sans-serif;
}

.detail-value {
  font-size: 0.875rem; /* 14px -> 0.875rem */
  color: #333;
  font-weight: 600;
  text-align: right;
  flex: 1;
  margin-left: 1rem; /* 16px -> 1rem */
  font-family: "Source Han Sans SC", sans-serif;
}

.detail-value.earnings {
  color: #ff4757;
  font-weight: 700;
}

.detail-value.vip {
  color: #ffa502;
  font-weight: 700;
}

/* 状态样式 */
.detail-value.status-active {
  color: #2ed573;
  font-weight: 700;
}

.detail-value.status-inactive {
  color: #ffa502;
  font-weight: 700;
}

.detail-value.status-suspended {
  color: #ff6b6b;
  font-weight: 700;
}

.detail-value.status-banned {
  color: #ff3838;
  font-weight: 700;
}

.detail-value.status-unknown {
  color: #999;
  font-weight: 500;
}

/* 滚动条样式 */
.popup-content::-webkit-scrollbar {
  width: 0.25rem; /* 4px -> 0.25rem */
}

.popup-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 0.125rem; /* 2px -> 0.125rem */
}

.popup-content::-webkit-scrollbar-thumb {
  background: #ff4757;
  border-radius: 0.125rem; /* 2px -> 0.125rem */
}

.popup-content::-webkit-scrollbar-thumb:hover {
  background: #d63031;
}

/* 收益详情弹窗样式 */
.earnings-detail-dialog .van-dialog__content {
  padding: 1.5rem; /* 24px -> 1.5rem */
  text-align: center;
}

.earnings-detail-dialog .van-dialog__message {
  font-size: 1rem; /* 16px -> 1rem */
  color: #333;
  font-weight: 500;
}

/* 收益项点击效果 */
.earnings-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.earnings-item:hover {
  transform: translateY(-0.125rem); /* -2px -> -0.125rem */
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1); /* 0 4px 12px -> 0 0.25rem 0.75rem */
}

.earnings-item:active {
  transform: translateY(0);
}
</style>
